apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-react-frontend-postgres
  namespace: ai-react-frontend-dev
  labels:
    app: ai-react-frontend-postgres
    component: database
    environment: dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ai-react-frontend-postgres
  template:
    metadata:
      labels:
        app: ai-react-frontend-postgres
        component: database
        environment: dev
    spec:
      containers:
      - name: postgres
        image: postgres:13
        ports:
        - containerPort: 5432
          name: postgres
        env:
        - name: POSTGRES_USER
          valueFrom:
            secretKeyRef:
              name: ai-react-frontend-secrets
              key: DB_USER
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: ai-react-frontend-secrets
              key: DB_PASSWORD
        - name: POSTGRES_DB
          value: ai_react_frontend
        - name: PGDATA
          value: /var/lib/postgresql/data/pgdata
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
        livenessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - postgres
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - postgres
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
      volumes:
      - name: postgres-storage
        persistentVolumeClaim:
          claimName: postgres-pvc
