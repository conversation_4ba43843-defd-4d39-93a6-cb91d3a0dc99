apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{PROJECT_ID}}
  namespace: {{NAMESPACE}}
  labels:
    app: {{PROJECT_ID}}
    component: {{APP_TYPE}}
    version: v1.0.0
    environment: {{ENVIRONMENT}}
spec:
  replicas: {{REPLICAS}}
  selector:
    matchLabels:
      app: {{PROJECT_ID}}
      app.kubernetes.io/name: {{PROJECT_ID}}
      app.kubernetes.io/version: "1.0.0"
      app.kubernetes.io/managed-by: argocd
  template:
    metadata:
      labels:
        app: {{PROJECT_ID}}
        app.kubernetes.io/name: {{PROJECT_ID}}
        app.kubernetes.io/version: "1.0.0"
        app.kubernetes.io/managed-by: argocd
        component: {{APP_TYPE}}
        version: v1.0.0
        environment: {{ENVIRONMENT}}
    spec:
      {{#eq APP_TYPE 'react-frontend'}}
      # React Frontend - No init containers needed (stateless)
      {{else}}
      {{#if ENABLE_DATABASE}}
      # Backend Applications - Database init container
      initContainers:
      - name: wait-for-postgres
        image: postgres:13-alpine
        command: ['sh', '-c']
        args:
        - |
          until pg_isready -h {{#if DB_HOST}}{{DB_HOST}}{{else}}{{PROJECT_ID}}-postgres{{/if}} -p 5432 -U {{DB_USER}}; do
            echo "Waiting for PostgreSQL to be ready..."
            sleep 2
          done
          echo "PostgreSQL is ready!"
        env:
        - name: PGPASSWORD
          valueFrom:
            secretKeyRef:
              name: {{PROJECT_ID}}-secrets
              key: DB_PASSWORD
      {{/if}}
      {{/eq}}
      containers:
      - name: {{PROJECT_ID}}
        image: {{CONTAINER_IMAGE}}
        imagePullPolicy: Always
        ports:
        - containerPort: {{CONTAINER_PORT}}
          name: http
        # Environment Variables from ConfigMap
        envFrom:
        - configMapRef:
            name: {{PROJECT_ID}}-config
        {{#eq APP_TYPE 'react-frontend'}}
        # React Frontend - Minimal environment variables (build-time configs in ConfigMap)
        env: []
        {{else}}
        # Backend Applications - Full secret environment variables
        env:
        {{#eq APP_TYPE 'springboot-backend'}}
        # Spring Boot specific database configuration
        - name: SPRING_DATASOURCE_USERNAME
          valueFrom:
            secretKeyRef:
              name: {{PROJECT_ID}}-secrets
              key: DB_USER
        - name: SPRING_DATASOURCE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: {{PROJECT_ID}}-secrets
              key: DB_PASSWORD
        - name: SPRING_MAIL_USERNAME
          valueFrom:
            secretKeyRef:
              name: {{PROJECT_ID}}-secrets
              key: SMTP_USER
        - name: SPRING_MAIL_PASSWORD
          valueFrom:
            secretKeyRef:
              name: {{PROJECT_ID}}-secrets
              key: SMTP_PASS
        - name: SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_GOOGLE_CLIENT_ID
          valueFrom:
            secretKeyRef:
              name: {{PROJECT_ID}}-secrets
              key: GOOGLE_CLIENT_ID
        - name: SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_GOOGLE_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              name: {{PROJECT_ID}}-secrets
              key: GOOGLE_CLIENT_SECRET
        {{else}}
        # Common backend secrets for non-Spring Boot applications
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: {{PROJECT_ID}}-secrets
              key: JWT_SECRET
        - name: DB_USER
          valueFrom:
            secretKeyRef:
              name: {{PROJECT_ID}}-secrets
              key: DB_USER
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: {{PROJECT_ID}}-secrets
              key: DB_PASSWORD
        - name: SMTP_USER
          valueFrom:
            secretKeyRef:
              name: {{PROJECT_ID}}-secrets
              key: SMTP_USER
        - name: SMTP_PASS
          valueFrom:
            secretKeyRef:
              name: {{PROJECT_ID}}-secrets
              key: SMTP_PASS
        - name: GOOGLE_CLIENT_ID
          valueFrom:
            secretKeyRef:
              name: {{PROJECT_ID}}-secrets
              key: GOOGLE_CLIENT_ID
        - name: GOOGLE_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              name: {{PROJECT_ID}}-secrets
              key: GOOGLE_CLIENT_SECRET
        {{/eq}}
        {{/eq}}
        # Health Checks - Application Type Specific
        {{#eq APP_TYPE 'react-frontend'}}
        livenessProbe:
          httpGet:
            path: {{HEALTH_CHECK_PATH}}
            port: {{CONTAINER_PORT}}
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: {{HEALTH_CHECK_PATH}}
            port: {{CONTAINER_PORT}}
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 3
          failureThreshold: 3
        {{else}}
        {{#eq APP_TYPE 'springboot-backend'}}
        livenessProbe:
          httpGet:
            path: {{HEALTH_CHECK_PATH}}
            port: {{CONTAINER_PORT}}
          initialDelaySeconds: 90
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 5
        readinessProbe:
          httpGet:
            path: {{HEALTH_CHECK_PATH}}
            port: {{CONTAINER_PORT}}
          initialDelaySeconds: 60
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        {{else}}
        # Default health checks for other application types
        livenessProbe:
          httpGet:
            path: {{HEALTH_CHECK_PATH}}
            port: {{CONTAINER_PORT}}
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 5
        readinessProbe:
          httpGet:
            path: {{HEALTH_CHECK_PATH}}
            port: {{CONTAINER_PORT}}
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        {{/eq}}
        {{/eq}}
        resources:
          requests:
            memory: "{{MEMORY_REQUEST}}"
            cpu: "{{CPU_REQUEST}}"
          limits:
            memory: "{{MEMORY_LIMIT}}"
            cpu: "{{CPU_LIMIT}}"
